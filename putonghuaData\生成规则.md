### 普通话训练数据 JSON 规范（简版）

用于在 `putonghuaData/` 目录下新增单字训练数据文件，文件名为“汉字.json”（例如：`吃.json`）。

## 文件结构

```json
{
  "_id": "chi_xxxxxx",
  "character": "吃",
  "category": "ch",
  "triggerQuestion": [
    "中午你打算做什么？ || 吃饭",
    "面对艰苦的环境，你准备怎么对待？ || 吃苦",
    "这笔交易你觉得我们亏不亏？ || 吃亏"
  ],
  "words": [
    {
      "text": "吃饭",
      "pinyin": "chī fàn",
      "sentences": [
        { "text": "我们每天都要吃饭", "pinyin": "wǒ men měi tiān dōu yào chī fàn" },
        { "text": "按时吃饭对身体好", "pinyin": "àn shí chī fàn duì shēn tǐ hǎo" },
        { "text": "一家人在一起吃饭很温馨", "pinyin": "yī jiā rén zài yī qǐ chī fàn hěn wēn xīn" }
      ]
    }
  ],
  "createTime": "2025-01-01T00:00:00.000Z",
  "updateTime": "2025-01-01T00:00:00.000Z"
}
```

## 字段说明

- `_id`（必填，string）
  - 全局唯一。推荐：`<汉字拼音或标识>_<5~6位随机字母数字>`，如 `chi_a1b2c`。
- `character`（必填，string）
  - 单个汉字，必须与文件名一致（例如文件名 `吃.json` 则为 `"吃"`）。
- `category`（必填，string）
  - 规则：取该字拼音的声母/韵母。
  - 若声母属于 `zh | ch | sh | r | z | c | s`，则取该声母；否则取韵母（例：`月 yue → ue`，`热 re → r`，`吃 chi → ch`）。
- `triggerQuestion`（可选，string[]）
  - 多条“问题与答案”对，数组每项为一个字符串，格式：`问题 || 答案`。
  - 规范：
    - 问题：≤18字、口语化、具体；引导回答自然落到该字的常见词/成语。
    - 答案：必须包含目标汉字（词/成语/短语即可），建议 2~8 字。
    - 分隔符使用两个竖线：`||`（建议两侧各保留一个空格）。
    - 每个字建议提供 6~12 条。
  - 示例项：
    - `中午你打算做什么？ || 吃饭`
    - `面对艰苦的环境，你准备怎么对待？ || 吃苦`
    - `这笔交易你觉得我们亏不亏？ || 吃亏`
- `words`（必填，array）
  - 每个元素一个“包含该字的词/短语/成语”。
  - `text`（必填）：词语文本；必须包含 `character`。
  - `pinyin`（可选）：词语拼音（推荐使用带声调的标准拼音，词间空格，如 `chī fàn`）。
  - `sentences`（必填，array）：示例句 3 条为宜；每条包含：
    - `text`（必填）：中文例句，尽量口语化、场景自然、避免歧义和敏感内容。
    - `pinyin`（必填）：对应拼音（带声调，空格分词）。
- `createTime` / `updateTime`（可选，string）
  - ISO 8601 时间（例如 `2025-05-24T02:26:08.286Z`）。若不维护可省略；若填写需按 ISO 格式。

## 编写规则（精简）

- 文件命名：`<汉字>.json`，UTF-8 编码，结尾保留换行。
- `category` 填写法：
  - 取单字标准拼音；若声母在 `zh/ch/sh/r/z/c/s`，用该声母；否则写韵母（去掉开头的 `y/w` 引导音，如 `yue→ue`，`ying→ing`，`wen→en`）。
- 词语选择：
  - 覆盖 8~20 个常用搭配/词组/成语；均必须包含该汉字。
  - 优先高频、易懂、贴近生活/学习/工作/校园等中性情境。
  - 同义或语义高度重合的词尽量少选，保证多样性。
- 例句撰写：
  - 每词 3 句为宜；不超过 20 字；不使用生僻词；保持中立与安全。
  - 例句需与词语语义强相关，避免机械重复。
  - 必须补齐对应拼音（声调规范，词间空格）。
- 排序建议：
  - 先列通用词，再列固定搭配/成语，最后补充相对不常见用法。
- 数据一致性：
  - `character` 与文件名一致；所有 `words[].text` 均含该字。
  - `category` 合理（与读音匹配），同目录内保持风格一致。
  - `_id` 唯一且稳定；不要使用纯递增数字。

## 最小可用模板

将以下模板复制为新文件并按需填充：

```json
{
  "_id": "<pinyin_random>",
  "character": "<汉字>",
  "category": "<分类，按声母/韵母规则>",
  "triggerQuestion": [
    "<问题1> || <答案1 含该字>",
    "<问题2> || <答案2 含该字>"
  ],
  "words": [
    {
      "text": "<包含该字的词语>",
      "pinyin": "<拼音 可省略>",
      "sentences": [
        { "text": "<例句1>", "pinyin": "<拼音1>" },
        { "text": "<例句2>", "pinyin": "<拼音2>" },
        { "text": "<例句3>", "pinyin": "<拼音3>" }
      ]
    }
  ],
  "createTime": "<ISO8601 可省略>",
  "updateTime": "<ISO8601 可省略>"
}
```

## 校验清单

- 文件名/`character` 一致；`words[].text` 均含该字。
- `category` 按规则填写（`zh/ch/sh/r/z/c/s` 用声母；否则用韵母且去引导音）。
- 每个词至少 1 条句子（推荐 3 条），句子与拼音成对出现。
- JSON 缩进 2 空格；键名使用小驼峰；末尾保留换行。
- 时间字段若填写，符合 ISO 8601 标准。
 - `triggerQuestion` 每项均符合 `问题 || 答案`，答案含目标字；数组条数建议 6~12。

## 出题规则（triggerQuestion，短好答）

- 目标：问题短、具体，回答时自然落到“包含该字的高频词/成语”，尽量不直接出现该字（可视情况出现）。不超过18字。
- 选题步骤：
  1) 先从 `words[].text` 挑1个最常用/最代表性的搭配或成语
  2) 用下方“问法模板”改成一句口语化问题
  3) 让回答指向“怎么形容/叫什么/第一反应/后果如何/最受欢迎”等固定表达
  4) 多义时，优先口语高频义项
- 问法模板（挑一个最贴合的）：
  - 动作日常（如 吃饭）: “你打算做什么？”
  - 态度品质（如 吃苦）: “面对艰苦的环境，你准备怎么对待？”
  - 利益得失（如 吃亏）: “这笔交易你觉得我们亏不亏？”
  - 情绪反应（如 吃惊/吃醋）: “听到这个消息你第一反应是什么？”/“他看到你和别人聊天会有什么情绪？”
  - 受欢迎度（如 吃香）: “现在职场上哪个专业最受欢迎？”
  - 碰壁受挫（如 吃闭门羹）: “你去拜访客户被拒绝了，这事怎么形容？”
  - 法律风险（如 吃官司）: “合同纠纷升级后你最担心什么后果？”
  - 严重后果（如 吃不了兜着走）: “要是把这事闹大了，后果会怎样？”
  - 过度索取（如 吃干抹净）: “一桌菜被你们扫光了，你会怎么形容？”
  - 背叛不忠（如 吃里扒外）: “在团队里暗中出卖集体利益，这叫什么？”
  - 停滞不前（如 吃老本）: “老是依赖过去的成绩不学新东西，这种状态叫什么？”
  - 不劳而获（如 吃现成饭）: “不愿自己动手，总想着拿别人的成果，这叫什么？”
  - 感恩传统（如 吃水不忘挖井人）: “受过别人帮助后，应该抱有什么态度？”
  - 励志箴言（如 吃不了苦中苦…）: “不愿意吃苦还能成才吗？用一句话劝劝他。”
  - 以失败为师（如 吃一堑长一智）: “这次失败你会用哪句成语来总结教训？”
  - 游手好闲（如 吃闲饭）: “整天不工作靠别人养活，这种人怎么称呼？”
- 兜底：词条都生僻时，用“这叫什么/怎么形容？”类问法，引导回答成语或固定搭配。

### 示例：`月.json`

```json
{
  "_id": "yue_ab12c",
  "character": "月",
  "category": "ue",
  "triggerQuestion": [
    "什么时候的月亮最圆？ || 中秋月亮最圆",
    "晚上海边最容易看到什么？ || 月光"
  ],
  "words": [
    {
      "text": "月亮",
      "pinyin": "yuè liàng",
      "sentences": [
        { "text": "今晚的月亮很圆", "pinyin": "jīn wǎn de yuè liàng hěn yuán" },
        { "text": "我们一起看月亮", "pinyin": "wǒ men yī qǐ kàn yuè liàng" },
        { "text": "月亮从东边升起", "pinyin": "yuè liàng cóng dōng biān shēng qǐ" }
      ]
    }
  ]
}
```



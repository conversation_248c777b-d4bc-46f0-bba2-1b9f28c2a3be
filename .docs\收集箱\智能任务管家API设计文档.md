# 智能任务管家API设计文档

## 📋 概述

本文档定义了智能任务管家优化后的API接口设计，包括简化的状态管理、错误处理、智能功能等核心接口。

## 🔄 核心API变更

### 1. 简化的SSE消息格式

#### 1.1 新的消息类型定义
```javascript
// 简化后的SSE消息类型
export const SSE_MESSAGE_TYPES = {
  STATE_CHANGE: 'state_change',    // 状态变更
  CONTENT_CHUNK: 'content_chunk',  // 内容块
  TOOL_RESULT: 'tool_result',      // 工具结果
  ERROR: 'error'                   // 错误信息
}
```

#### 1.2 统一消息格式
```javascript
// 标准SSE消息格式
interface SSEMessage {
  type: string;           // 消息类型
  sessionId: string;      // 会话ID
  timestamp: string;      // 时间戳
  data: {
    state?: string;       // 当前状态
    content?: string;     // 内容
    progress?: number;    // 进度(0-100)
    result?: any;         // 结果数据
    error?: ErrorInfo;    // 错误信息
  };
}
```

### 2. 错误处理API

#### 2.1 统一错误响应格式
```javascript
// 标准错误响应
interface ErrorResponse {
  errCode: string;        // 错误码
  errMsg: string;         // 用户友好的错误信息
  errType: string;        // 错误类型: NETWORK|AUTH|PARSE|SYSTEM
  action: string;         // 建议操作: retry|login|rephrase|contact
  timestamp: string;      // 错误时间
  sessionId?: string;     // 会话ID
  details?: any;          // 详细错误信息(调试用)
}
```

#### 2.2 错误恢复API
```javascript
// POST /api/ai/recover-error
interface RecoverErrorRequest {
  sessionId: string;      // 原会话ID
  errorType: string;      // 错误类型
  retryAction: string;    // 重试动作
  userInput?: string;     // 用户重新输入的内容
}

interface RecoverErrorResponse {
  errCode: number;
  errMsg: string;
  data: {
    newSessionId: string; // 新会话ID
    recovered: boolean;   // 是否恢复成功
    suggestion?: string;  // 恢复建议
  };
}
```

## 🧠 智能功能API

### 1. 智能任务模板API

#### 1.1 获取推荐模板
```javascript
// GET /api/ai/templates/recommend
interface TemplateRecommendRequest {
  userInput: string;      // 用户输入
  userId?: string;        // 用户ID(用于个性化)
  limit?: number;         // 返回数量限制
}

interface TemplateRecommendResponse {
  errCode: number;
  errMsg: string;
  data: {
    templates: Array<{
      id: string;         // 模板ID
      name: string;       // 模板名称
      confidence: number; // 匹配置信度(0-1)
      settings: {         // 默认设置
        duration?: string;
        reminder?: string;
        priority?: string;
        tags?: string[];
      };
      preview: string;    // 预览文本
    }>;
    matchedKeywords: string[]; // 匹配的关键词
  };
}
```

#### 1.2 应用模板创建任务
```javascript
// POST /api/ai/tasks/create-with-template
interface CreateTaskWithTemplateRequest {
  templateId: string;     // 模板ID
  userInput: string;      // 用户原始输入
  customSettings?: any;   // 用户自定义设置
  applyAll?: boolean;     // 是否应用所有默认设置
}
```

### 2. 批量任务处理API

#### 2.1 批量任务解析
```javascript
// POST /api/ai/tasks/parse-batch
interface BatchTaskParseRequest {
  userInput: string;      // 用户输入
  parseMode?: string;     // 解析模式: auto|strict|loose
}

interface BatchTaskParseResponse {
  errCode: number;
  errMsg: string;
  data: {
    tasks: Array<{
      title: string;      // 任务标题
      time?: string;      // 时间
      type?: string;      // 任务类型
      confidence: number; // 解析置信度
      settings?: any;     // 推荐设置
    }>;
    totalCount: number;   // 解析出的任务总数
    highConfidenceCount: number; // 高置信度任务数
  };
}
```

#### 2.2 批量任务确认创建
```javascript
// POST /api/ai/tasks/create-batch
interface CreateBatchTasksRequest {
  tasks: Array<{
    title: string;
    settings: any;        // 任务设置
    confirmed: boolean;   // 用户是否确认
  }>;
  sessionId: string;      // 会话ID
}

interface CreateBatchTasksResponse {
  errCode: number;
  errMsg: string;
  data: {
    created: Array<{      // 成功创建的任务
      taskId: string;
      title: string;
      status: 'success';
    }>;
    failed: Array<{       // 创建失败的任务
      title: string;
      error: string;
      status: 'failed';
    }>;
    summary: {
      total: number;
      success: number;
      failed: number;
    };
  };
}
```

### 3. 用户偏好学习API

#### 3.1 记录用户偏好
```javascript
// POST /api/ai/preferences/learn
interface LearnPreferenceRequest {
  userId: string;         // 用户ID
  action: string;         // 用户行为: create|modify|delete
  context: {              // 上下文信息
    taskType?: string;
    timePreference?: string;
    settingsUsed?: any;
  };
  feedback?: {            // 用户反馈
    satisfied: boolean;
    rating?: number;      // 1-5分
    comment?: string;
  };
}
```

#### 3.2 获取个性化建议
```javascript
// GET /api/ai/preferences/suggestions
interface PreferenceSuggestionsRequest {
  userId: string;
  taskType?: string;      // 任务类型
  timeContext?: string;   // 时间上下文
}

interface PreferenceSuggestionsResponse {
  errCode: number;
  errMsg: string;
  data: {
    timePreference: string;     // 推荐时间
    defaultProject: string;     // 默认项目
    reminderStyle: string;      // 提醒方式
    priorityPattern: string;    // 优先级模式
    confidence: number;         // 推荐置信度
    basedOnHistory: {           // 基于历史数据
      sampleSize: number;
      lastUpdated: string;
    };
  };
}
```

## 🔧 系统优化API

### 1. 性能监控API

#### 1.1 获取系统性能指标
```javascript
// GET /api/ai/system/metrics
interface SystemMetricsResponse {
  errCode: number;
  errMsg: string;
  data: {
    performance: {
      avgResponseTime: number;    // 平均响应时间(ms)
      cacheHitRate: number;      // 缓存命中率(%)
      compressionRatio: number;   // 消息压缩比(%)
      errorRate: number;         // 错误率(%)
    };
    usage: {
      totalSessions: number;     // 总会话数
      activeSessions: number;    // 活跃会话数
      totalTasks: number;        // 总任务数
      successRate: number;       // 任务创建成功率(%)
    };
    health: {
      status: string;           // 系统状态: healthy|warning|error
      uptime: number;           // 运行时间(ms)
      lastCheck: string;        // 最后检查时间
    };
  };
}
```

### 2. 缓存管理API

#### 2.1 清理缓存
```javascript
// POST /api/ai/cache/clear
interface ClearCacheRequest {
  cacheType: string;      // 缓存类型: auth|templates|preferences|all
  userId?: string;        // 特定用户(可选)
}

interface ClearCacheResponse {
  errCode: number;
  errMsg: string;
  data: {
    cleared: string[];    // 已清理的缓存类型
    affectedCount: number; // 影响的记录数
  };
}
```

## 📱 前端状态管理API

### 1. 简化的状态接口

#### 1.1 状态管理器
```javascript
// 前端状态管理接口
interface AIStateManager {
  // 当前状态
  currentState: 'idle' | 'thinking' | 'executing' | 'responding';
  
  // 状态变更
  setState(newState: string, message?: string, progress?: number): void;
  
  // 获取状态信息
  getStateInfo(): {
    state: string;
    message: string;
    progress: number;
    duration: number;    // 当前状态持续时间
  };
  
  // 状态监听
  onStateChange(callback: (state: StateInfo) => void): void;
  
  // 重置状态
  reset(): void;
}
```

#### 1.2 错误处理器
```javascript
// 前端错误处理接口
interface ErrorHandler {
  // 处理错误
  handleError(error: ErrorResponse): Promise<{
    handled: boolean;
    action?: string;
    retry?: () => Promise<void>;
  }>;
  
  // 显示用户友好错误
  showUserError(errorType: string, message: string, actions?: Array<{
    text: string;
    action: () => void;
  }>): void;
  
  // 自动恢复
  autoRecover(errorType: string, context: any): Promise<boolean>;
}
```

## 🧪 测试API

### 1. 功能测试接口

#### 1.1 模拟对话测试
```javascript
// POST /api/ai/test/simulate-chat
interface SimulateChatRequest {
  scenarios: Array<{
    userInput: string;
    expectedState: string;
    expectedActions: string[];
    timeout?: number;
  }>;
  testMode: boolean;      // 测试模式标志
}
```

#### 1.2 性能压力测试
```javascript
// POST /api/ai/test/stress
interface StressTestRequest {
  concurrentUsers: number;  // 并发用户数
  duration: number;         // 测试时长(秒)
  scenario: string;         // 测试场景
}
```

## 📊 数据分析API

### 1. 用户行为分析

#### 1.1 获取使用统计
```javascript
// GET /api/ai/analytics/usage
interface UsageAnalyticsResponse {
  errCode: number;
  errMsg: string;
  data: {
    dailyStats: Array<{
      date: string;
      sessions: number;
      tasks: number;
      successRate: number;
    }>;
    topFeatures: Array<{
      feature: string;
      usage: number;
      satisfaction: number;
    }>;
    userPatterns: {
      peakHours: number[];
      commonTaskTypes: string[];
      averageSessionLength: number;
    };
  };
}
```

## 🔐 安全与权限

### 1. API访问控制

#### 1.1 认证接口
```javascript
// POST /api/ai/auth/validate
interface AuthValidateRequest {
  token: string;
  sessionId?: string;
}

interface AuthValidateResponse {
  errCode: number;
  errMsg: string;
  data: {
    valid: boolean;
    userId: string;
    permissions: string[];
    expiresAt: string;
  };
}
```

## 📝 接口版本管理

### 1. 版本兼容性

#### 1.1 版本信息
```javascript
// GET /api/ai/version
interface VersionResponse {
  errCode: number;
  errMsg: string;
  data: {
    version: string;        // 当前版本
    apiVersion: string;     // API版本
    features: string[];     // 支持的功能
    deprecated: string[];   // 已废弃的接口
    migration?: {           // 迁移指南
      from: string;
      to: string;
      guide: string;
    };
  };
}
```

这个API设计文档涵盖了智能任务管家优化的所有核心接口，确保前后端协作顺畅，同时为未来扩展预留了空间。

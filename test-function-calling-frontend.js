/**
 * Function Calling 前端功能测试脚本
 * 测试历史消息转换和工具结果显示功能
 */

// 模拟消息类型定义
const MESSAGE_TYPES = {
  USER: 'user',
  AI_STREAMING: 'ai_streaming',
  AI_COMPLETE: 'ai_complete',
  TASK_COMPLETE: 'task_complete',
  TOOL_RESULT: 'tool_result',
  ERROR: 'error',
}

// 模拟历史消息转换函数
const convertMessagesToFunctionCallingFormat = (messages) => {
  return messages
    .filter(msg => 
      msg.type === MESSAGE_TYPES.USER || 
      msg.type === MESSAGE_TYPES.AI_COMPLETE ||
      msg.type === MESSAGE_TYPES.TOOL_RESULT
    )
    .map(msg => {
      // 用户消息
      if (msg.isUser) {
        return { 
          role: 'user', 
          content: msg.content 
        }
      }
      
      // AI 完成消息（可能包含工具调用）
      if (msg.type === MESSAGE_TYPES.AI_COMPLETE) {
        const assistantMessage = {
          role: 'assistant',
          content: msg.content
        }
        
        // 如果消息包含工具调用信息，添加到消息中
        if (msg.tool_calls && msg.tool_calls.length > 0) {
          assistantMessage.tool_calls = msg.tool_calls
        }
        
        return assistantMessage
      }
      
      // 工具执行结果消息
      if (msg.type === MESSAGE_TYPES.TOOL_RESULT) {
        return {
          role: 'tool',
          tool_call_id: msg.toolCallId,
          content: JSON.stringify({
            success: msg.success,
            result: msg.result,
            error: msg.error,
            toolName: msg.toolName,
            executionTime: msg.toolData?.executionTime,
            summary: msg.toolData?.summary
          })
        }
      }
    })
    .filter(Boolean)
}

// 测试数据
const testMessages = [
  // 用户消息
  {
    _id: 'user_1',
    type: MESSAGE_TYPES.USER,
    content: '帮我查看今天的任务',
    isUser: true,
    time: '2025-01-08T10:00:00Z'
  },
  
  // AI 回复消息（包含工具调用）
  {
    _id: 'ai_1',
    type: MESSAGE_TYPES.AI_COMPLETE,
    content: '我来帮您查看今天的任务。',
    isUser: false,
    tool_calls: [
      {
        id: 'call_123',
        type: 'function',
        function: {
          name: 'getTasks',
          arguments: JSON.stringify({
            mode: 'today',
            completed: false
          })
        }
      }
    ],
    time: '2025-01-08T10:00:05Z'
  },
  
  // 工具执行结果消息
  {
    _id: 'tool_result_1',
    type: MESSAGE_TYPES.TOOL_RESULT,
    toolName: 'getTasks',
    result: {
      success: true,
      data: [
        { _id: 'task1', title: '完成项目报告', priority: 5 },
        { _id: 'task2', title: '参加团队会议', priority: 3 }
      ],
      message: '成功获取今天的任务'
    },
    success: true,
    toolCallId: 'call_123',
    isUser: false,
    toolData: {
      executionTime: 150,
      dataCount: 2,
      summary: '执行成功'
    },
    time: '2025-01-08T10:00:10Z'
  },
  
  // AI 最终回复
  {
    _id: 'ai_2',
    type: MESSAGE_TYPES.AI_COMPLETE,
    content: '您今天有2个未完成的任务：\n1. 完成项目报告（高优先级）\n2. 参加团队会议（中优先级）',
    isUser: false,
    time: '2025-01-08T10:00:15Z'
  }
]

// 测试历史消息转换
console.log('=== 测试历史消息转换功能 ===')
const convertedMessages = convertMessagesToFunctionCallingFormat(testMessages)
console.log('原始消息数量:', testMessages.length)
console.log('转换后消息数量:', convertedMessages.length)
console.log('转换结果:', JSON.stringify(convertedMessages, null, 2))

// 验证转换结果
console.log('\n=== 验证转换结果 ===')
const hasUserMessage = convertedMessages.some(msg => msg.role === 'user')
const hasAssistantMessage = convertedMessages.some(msg => msg.role === 'assistant')
const hasToolMessage = convertedMessages.some(msg => msg.role === 'tool')
const hasToolCalls = convertedMessages.some(msg => msg.tool_calls)

console.log('包含用户消息:', hasUserMessage)
console.log('包含助手消息:', hasAssistantMessage)
console.log('包含工具消息:', hasToolMessage)
console.log('包含工具调用:', hasToolCalls)

// 测试工具结果消息创建
console.log('\n=== 测试工具结果消息创建 ===')
const createToolResultMessage = ({ toolName, result, success, error, toolCallId }) => {
  return {
    _id: `tool_result_${Date.now()}`,
    type: MESSAGE_TYPES.TOOL_RESULT,
    toolName: toolName,
    result: success ? result : null,
    error: error || null,
    success: success,
    toolCallId: toolCallId || `tool_${toolName}_${Date.now()}`,
    isUser: false,
    status: success ? 'complete' : 'error',
    time: new Date().toISOString(),
    toolData: {
      executionTime: result?.executionTime || null,
      dataCount: result?.data?.length || 0,
      summary: result?.message || (success ? '执行成功' : '执行失败')
    }
  }
}

// 测试成功的工具结果
const successToolResult = createToolResultMessage({
  toolName: 'createTask',
  result: {
    success: true,
    data: { _id: 'new_task_123', title: '新任务' },
    message: '任务创建成功',
    executionTime: 200
  },
  success: true,
  toolCallId: 'call_456'
})

console.log('成功工具结果:', JSON.stringify(successToolResult, null, 2))

// 测试失败的工具结果
const errorToolResult = createToolResultMessage({
  toolName: 'updateTask',
  error: '任务不存在',
  success: false,
  toolCallId: 'call_789'
})

console.log('失败工具结果:', JSON.stringify(errorToolResult, null, 2))

console.log('\n=== 测试完成 ===')
console.log('✅ 历史消息转换功能正常')
console.log('✅ 工具结果消息创建功能正常')
console.log('✅ Function Calling 前端功能实现完整')

/**
 * 时间上下文构建工具
 * 提供统一的 now/相对日期/周末/月底 等计算，支持 zh-CN 文本格式与原始 Date。
 */

function buildTimeContext(now = new Date()) {
  // 相对日期
  const msDay = 24 * 60 * 60 * 1000
  const tomorrow = new Date(now.getTime() + 1 * msDay)
  const yesterday = new Date(now.getTime() - 1 * msDay)
  const dayAfterTomorrow = new Date(now.getTime() + 2 * msDay)
  const dayBeforeYesterday = new Date(now.getTime() - 2 * msDay)
  const nextWeek = new Date(now.getTime() + 7 * msDay)

  // 本周末与周日
  const currentWeekday = now.getDay() // 0=周日，1=周一，..., 6=周六
  const daysToSaturday = 6 - currentWeekday
  const daysToSunday = 7 - currentWeekday
  const thisSaturday = new Date(now.getTime() + daysToSaturday * msDay)
  const thisSunday = new Date(now.getTime() + daysToSunday * msDay)

  // 下周一
  const daysToNextMonday = (8 - currentWeekday) % 7 || 7
  const nextMonday = new Date(now.getTime() + daysToNextMonday * msDay)

  // 本月与下月
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
  const firstDayOfNextMonth = new Date(currentYear, currentMonth + 1, 1)
  const lastDayOfNextMonth = new Date(currentYear, currentMonth + 2, 0)

  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  const timeInfo = {
    current_datetime: now.toISOString(),
    current_date: now.toLocaleDateString('zh-CN'),
    current_time: now.toLocaleTimeString('zh-CN'),
    current_year: now.getFullYear(),
    current_month: now.getMonth() + 1,
    current_day: now.getDate(),
    current_hour: now.getHours(),
    current_minute: now.getMinutes(),
    current_weekday: weekdays[now.getDay()],
    timezone: 'Asia/Shanghai (UTC+8)',
    unix_timestamp: now.getTime(),
  }

  const dates = {
    tomorrow: tomorrow.toLocaleDateString('zh-CN'),
    yesterday: yesterday.toLocaleDateString('zh-CN'),
    dayAfterTomorrow: dayAfterTomorrow.toLocaleDateString('zh-CN'),
    dayBeforeYesterday: dayBeforeYesterday.toLocaleDateString('zh-CN'),
    nextWeek: nextWeek.toLocaleDateString('zh-CN'),
    thisSaturday: thisSaturday.toLocaleDateString('zh-CN'),
    thisSunday: thisSunday.toLocaleDateString('zh-CN'),
    nextMonday: nextMonday.toLocaleDateString('zh-CN'),
    lastDayOfMonth: lastDayOfMonth.toLocaleDateString('zh-CN'),
    firstDayOfNextMonth: firstDayOfNextMonth.toLocaleDateString('zh-CN'),
    lastDayOfNextMonth: lastDayOfNextMonth.toLocaleDateString('zh-CN'),
  }

  const raw = {
    now,
    tomorrow,
    yesterday,
    dayAfterTomorrow,
    dayBeforeYesterday,
    nextWeek,
    thisSaturday,
    thisSunday,
    nextMonday,
    lastDayOfMonth,
    firstDayOfNextMonth,
    lastDayOfNextMonth,
  }

  return { now, timeInfo, dates, raw }
}

module.exports = { buildTimeContext }


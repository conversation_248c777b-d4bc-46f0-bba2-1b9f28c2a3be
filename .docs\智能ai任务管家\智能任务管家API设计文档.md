# 智能任务管家 API 设计文档

## 📋 概述

本文档定义了智能任务管家优化后的 API 接口设计，重点关注简化的状态管理、错误处理、智能功能等核心接口。**开发阶段无需考虑向后兼容**。

## 🔄 核心 API 变更

### 1. 简化的 SSE 消息格式

#### 1.1 新的消息类型定义

```javascript
// 简化后的SSE消息类型（从10种减少到4种）
export const SSE_MESSAGE_TYPES = {
  STATE_CHANGE: 'state_change', // 状态变更
  CONTENT_CHUNK: 'content_chunk', // 内容块
  TOOL_RESULT: 'tool_result', // 工具结果
  ERROR: 'error', // 错误信息
}
```

#### 1.2 统一消息格式

```javascript
// 标准SSE消息格式
interface SSEMessage {
  type: string; // 消息类型
  sessionId: string; // 会话ID
  timestamp: string; // 时间戳
  data: {
    state?: 'idle' | 'thinking' | 'executing' | 'responding',
    content?: string, // 内容
    progress?: number, // 进度(0-100)
    result?: any, // 结果数据
    error?: ErrorInfo, // 错误信息
    message?: string, // 用户友好的状态描述
  };
}
```

### 2. 错误处理 API

#### 2.1 统一错误响应格式

```javascript
// 标准错误响应
interface ErrorResponse {
  errCode: string; // 错误码
  errMsg: string; // 用户友好的错误信息
  errType: 'NETWORK' | 'AUTH' | 'PARSE' | 'SYSTEM';
  action: 'retry' | 'login' | 'rephrase' | 'contact';
  timestamp: string; // 错误时间
  sessionId?: string; // 会话ID
  actionButton?: {
    // 操作按钮配置
    text: string,
    icon: string,
    handler: string,
  };
}
```

#### 2.2 错误恢复 API

```javascript
// POST /api/ai/recover-error
interface RecoverErrorRequest {
  sessionId: string; // 原会话ID
  errorType: string; // 错误类型
  retryAction: string; // 重试动作
  userInput?: string; // 用户重新输入的内容
}

interface RecoverErrorResponse {
  errCode: number;
  errMsg: string;
  data: {
    newSessionId: string, // 新会话ID
    recovered: boolean, // 是否恢复成功
    suggestion?: string, // 恢复建议
  };
}
```

## 🧠 智能功能 API

### 1. 智能任务模板 API

#### 1.1 获取推荐模板

```javascript
// GET /api/ai/templates/recommend
interface TemplateRecommendRequest {
  userInput: string; // 用户输入
  userId?: string; // 用户ID(用于个性化)
  limit?: number; // 返回数量限制，默认3
}

interface TemplateRecommendResponse {
  errCode: number;
  errMsg: string;
  data: {
    templates: Array<{
      id: string, // 模板ID
      name: string, // 模板名称
      confidence: number, // 匹配置信度(0-1)
      settings: {
        // 默认设置
        duration?: string,
        reminder?: string,
        priority?: 'low' | 'medium' | 'high',
        tags?: string[],
      },
      preview: string, // 预览文本
    }>,
    matchedKeywords: string[], // 匹配的关键词
  };
}
```

#### 1.2 应用模板创建任务

```javascript
// POST /api/ai/tasks/create-with-template
interface CreateTaskWithTemplateRequest {
  templateId: string; // 模板ID
  userInput: string; // 用户原始输入
  customSettings?: any; // 用户自定义设置
  applyAll?: boolean; // 是否应用所有默认设置
}
```

### 2. 批量任务处理 API

#### 2.1 批量任务解析

```javascript
// POST /api/ai/tasks/parse-batch
interface BatchTaskParseRequest {
  userInput: string; // 用户输入
  parseMode?: 'auto' | 'strict' | 'loose'; // 解析模式，默认auto
}

interface BatchTaskParseResponse {
  errCode: number;
  errMsg: string;
  data: {
    tasks: Array<{
      title: string, // 任务标题
      time?: string, // 时间
      type?: string, // 任务类型
      confidence: number, // 解析置信度(0-1)
      settings?: any, // 推荐设置
    }>,
    totalCount: number, // 解析出的任务总数
    highConfidenceCount: number, // 高置信度任务数(>0.8)
  };
}
```

#### 2.2 批量任务确认创建

```javascript
// POST /api/ai/tasks/create-batch
interface CreateBatchTasksRequest {
  tasks: Array<{
    title: string,
    settings: any, // 任务设置
    confirmed: boolean, // 用户是否确认
  }>;
  sessionId: string; // 会话ID
}

interface CreateBatchTasksResponse {
  errCode: number;
  errMsg: string;
  data: {
    created: Array<{
      // 成功创建的任务
      taskId: string,
      title: string,
      status: 'success',
    }>,
    failed: Array<{
      // 创建失败的任务
      title: string,
      error: string,
      status: 'failed',
    }>,
    summary: {
      total: number,
      success: number,
      failed: number,
    },
  };
}
```

### 3. 用户偏好学习 API

#### 3.1 记录用户偏好

```javascript
// POST /api/ai/preferences/learn
interface LearnPreferenceRequest {
  userId: string; // 用户ID
  action: 'create' | 'modify' | 'delete'; // 用户行为
  context: {
    // 上下文信息
    taskType?: string,
    timePreference?: string,
    settingsUsed?: any,
  };
  feedback?: {
    // 用户反馈
    satisfied: boolean,
    rating?: number, // 1-5分
    comment?: string,
  };
}
```

#### 3.2 获取个性化建议

```javascript
// GET /api/ai/preferences/suggestions
interface PreferenceSuggestionsRequest {
  userId: string;
  taskType?: string; // 任务类型
  timeContext?: string; // 时间上下文
}

interface PreferenceSuggestionsResponse {
  errCode: number;
  errMsg: string;
  data: {
    timePreference: string, // 推荐时间
    defaultProject: string, // 默认项目
    reminderStyle: string, // 提醒方式
    priorityPattern: string, // 优先级模式
    confidence: number, // 推荐置信度
    basedOnHistory: {
      // 基于历史数据
      sampleSize: number,
      lastUpdated: string,
    },
  };
}
```

## 🤖 P1.5 智能工作伙伴 API

### 1. OKR 上下文关联 API

#### 1.1 获取 OKR 上下文

```javascript
// GET /api/ai/okr/context
interface OKRContextRequest {
  userId: string;
  taskKeywords?: string[]; // 任务关键词，用于匹配相关目标
}

interface OKRContextResponse {
  errCode: number;
  errMsg: string;
  data: {
    currentObjectives: Array<{
      id: string,
      title: string,
      progress: number,
      keyResults: Array<{
        id: string,
        title: string,
        progress: number,
      }>,
    }>,
    suggestedObjective?: {
      id: string,
      title: string,
      confidence: number,
      reason: string,
    },
  };
}
```

#### 1.2 关联任务到 OKR

```javascript
// POST /api/ai/okr/associate-task
interface AssociateTaskRequest {
  taskId: string;
  objectiveId: string;
  contributionLevel: 'high' | 'medium' | 'low';
  expectedImpact?: string;
}
```

### 2. 智能日报生成 API

#### 2.1 生成日报

```javascript
// POST /api/ai/reports/daily
interface GenerateDailyReportRequest {
  userId: string;
  date: string; // YYYY-MM-DD格式
  includeInsights?: boolean; // 是否包含智能洞察
}

interface GenerateDailyReportResponse {
  errCode: number;
  errMsg: string;
  data: {
    summary: {
      tasksCompleted: number,
      tasksCreated: number,
      focusTime: string,
      topAchievement: string,
    },
    objectives: Array<{
      title: string,
      todayProgress: number,
      completedTasks: string[],
      nextSteps: string[],
    }>,
    insights: string[],
    trends: {
      efficiencyTrend: 'up' | 'down' | 'stable',
      focusAreas: string[],
      recommendations: string[],
    },
  };
}
```

### 3. 情境感知任务拓展 API

#### 3.1 拓展任务

```javascript
// POST /api/ai/tasks/expand
interface ExpandTaskRequest {
  userInput: string; // 用户原始输入
  context: {
    currentObjective?: string,
    emotionalState?: 'focused' | 'stressed' | 'creative' | 'tired',
    workingHours?: 'peak' | 'normal' | 'low',
    recentTasks?: string[],
  };
  expansionLevel?: 'basic' | 'detailed' | 'comprehensive';
}

interface ExpandTaskResponse {
  errCode: number;
  errMsg: string;
  data: {
    originalInput: string,
    expandedTask: {
      title: string,
      description: string,
      subtasks?: string[],
      estimatedTime?: string,
      priority?: 'low' | 'medium' | 'high',
      relatedOKR?: string,
      tags?: string[],
    },
    confidence: number,
    expansionReason: string,
  };
}
```

### 4. 智能任务整理 API

#### 4.1 分析任务结构

```javascript
// POST /api/ai/tasks/analyze
interface AnalyzeTasksRequest {
  userId: string;
  taskIds?: string[]; // 指定任务ID，为空则分析所有任务
  analysisType?: 'duplicates' | 'dependencies' | 'priorities' | 'all';
}

interface AnalyzeTasksResponse {
  errCode: number;
  errMsg: string;
  data: {
    duplicateTasks: Array<{
      tasks: string[],
      similarity: number,
      suggestion: string,
    }>,
    missingDependencies: Array<{
      task: string,
      dependency: string,
      suggestion: string,
    }>,
    priorityConflicts: Array<{
      conflict: string,
      suggestion: string,
    }>,
    categoryOptimization: Array<{
      suggestion: string,
      affectedTasks: string[],
    }>,
  };
}
```

#### 4.2 执行任务整理

```javascript
// POST /api/ai/tasks/organize
interface OrganizeTasksRequest {
  userId: string;
  operations: Array<{
    type: 'merge' | 'categorize' | 'prioritize' | 'add_dependency',
    taskIds: string[],
    parameters: any,
  }>;
  autoConfirm?: boolean; // 是否自动确认所有操作
}

interface OrganizeTasksResponse {
  errCode: number;
  errMsg: string;
  data: {
    executedOperations: Array<{
      type: string,
      success: boolean,
      affectedTasks: string[],
      result?: string,
      error?: string,
    }>,
    summary: {
      totalOperations: number,
      successfulOperations: number,
      failedOperations: number,
    },
  };
}
```

## 📱 前端状态管理 API

### 1. 简化的状态接口

#### 1.1 状态管理器

```javascript
// 前端状态管理接口
interface AIStateManager {
  // 当前状态
  currentState: 'idle' | 'thinking' | 'executing' | 'responding';

  // 状态变更
  setState(newState: string, message?: string, progress?: number): void;

  // 获取状态信息
  getStateInfo(): {
    state: string,
    message: string,
    progress: number,
    duration: number, // 当前状态持续时间
  };

  // 状态监听
  onStateChange(callback: (state: StateInfo) => void): void;

  // 重置状态
  reset(): void;

  // 状态转换验证
  canTransition(from: string, to: string): boolean;
}
```

#### 1.2 错误处理器

```javascript
// 前端错误处理接口
interface ErrorHandler {
  // 处理错误
  handleError(error: ErrorResponse): Promise<{
    handled: boolean,
    action?: string,
    retry?: () => Promise<void>,
  }>;

  // 显示用户友好错误
  showUserError(
    errorType: string,
    message: string,
    actions?: Array<{
      text: string,
      icon: string,
      action: () => void,
    }>
  ): void;

  // 自动恢复
  autoRecover(errorType: string, context: any): Promise<boolean>;

  // 错误统计
  getErrorStats(): {
    totalErrors: number,
    recoveredErrors: number,
    errorTypes: Record<string, number>,
  };
}
```

## 🔧 系统优化 API

### 1. 性能监控 API

#### 1.1 获取系统性能指标

```javascript
// GET /api/ai/system/metrics
interface SystemMetricsResponse {
  errCode: number;
  errMsg: string;
  data: {
    performance: {
      avgResponseTime: number, // 平均响应时间(ms)
      stateTransitionTime: number, // 状态转换平均耗时(ms)
      errorRate: number, // 错误率(%)
      cacheHitRate: number, // 缓存命中率(%)
    },
    usage: {
      totalSessions: number, // 总会话数
      activeSessions: number, // 活跃会话数
      totalTasks: number, // 总任务数
      successRate: number, // 任务创建成功率(%)
    },
    health: {
      status: 'healthy' | 'warning' | 'error',
      uptime: number, // 运行时间(ms)
      lastCheck: string, // 最后检查时间
    },
  };
}
```

### 2. 缓存管理 API

#### 2.1 清理缓存

```javascript
// POST /api/ai/cache/clear
interface ClearCacheRequest {
  cacheType: 'templates' | 'preferences' | 'all';
  userId?: string; // 特定用户(可选)
}

interface ClearCacheResponse {
  errCode: number;
  errMsg: string;
  data: {
    cleared: string[], // 已清理的缓存类型
    affectedCount: number, // 影响的记录数
  };
}
```

## 🧪 测试 API

### 1. 功能测试接口

#### 1.1 模拟对话测试

```javascript
// POST /api/ai/test/simulate-chat
interface SimulateChatRequest {
  scenarios: Array<{
    userInput: string,
    expectedState: string,
    expectedActions: string[],
    timeout?: number,
  }>;
  testMode: boolean; // 测试模式标志
}

interface SimulateChatResponse {
  errCode: number;
  errMsg: string;
  data: {
    results: Array<{
      scenario: string,
      passed: boolean,
      actualState: string,
      actualActions: string[],
      duration: number,
      error?: string,
    }>,
    summary: {
      total: number,
      passed: number,
      failed: number,
      avgDuration: number,
    },
  };
}
```

## 📊 数据分析 API

### 1. 用户行为分析

#### 1.1 获取使用统计

```javascript
// GET /api/ai/analytics/usage
interface UsageAnalyticsResponse {
  errCode: number;
  errMsg: string;
  data: {
    dailyStats: Array<{
      date: string,
      sessions: number,
      tasks: number,
      successRate: number,
    }>,
    topFeatures: Array<{
      feature: string,
      usage: number,
      satisfaction: number,
    }>,
    userPatterns: {
      peakHours: number[],
      commonTaskTypes: string[],
      averageSessionLength: number,
    },
  };
}
```

## 🔐 安全与权限

### 1. API 访问控制

#### 1.1 认证接口

```javascript
// POST /api/ai/auth/validate
interface AuthValidateRequest {
  token: string;
  sessionId?: string;
}

interface AuthValidateResponse {
  errCode: number;
  errMsg: string;
  data: {
    valid: boolean,
    userId: string,
    permissions: string[],
    expiresAt: string,
  };
}
```

这个 API 设计文档涵盖了智能任务管家优化的所有核心接口，采用直接重构策略，无需考虑向后兼容，确保前后端协作顺畅。
